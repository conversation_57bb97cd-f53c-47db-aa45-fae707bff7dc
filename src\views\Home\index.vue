<!--
 * @Description: 首页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2025-04-08 14:02:17
-->
<script lang="ts">
  import { specialTenantList, unepTenantList } from "@/utils/constant"
  export default {
    name: "Home",
    inheritAttrs: false
  }
</script>

<script lang="ts" setup>
  import HomeBanner from "./components/home-banner.vue"
  import HomeRecommend from "./components/home-recommend.vue"
  import HomeHot from "./components/home-hot.vue"
  import HomeTrailer from "./components/home-trailer.vue"
  import LearningRanking from "./components/learning-ranking.vue"
  import PreventDisasterHome from "./unep/prevent-disaster-home.vue"
  import Safetydevelopment from "./town/safety-development.vue"
  import CjaqHome from "./cjaq/cjaq-home.vue"
  import JdHome from "./jd/jd-home.vue"
  import PtHome from "./pt/pt-home.vue"
  // import PthtgHome from "./pthtg/pthtg-home.vue"
  import useTenantStore from "@/store/modules/tenant"
  import { getToken } from "@/utils/auth"
  import useUserStore from "@/store/modules/user"

  const userStore = useUserStore()
  const { domainName } = storeToRefs(useTenantStore())
  const token = ref(userStore.token || getToken())
  
  // 计算是否显示学习排行榜
  const shouldShowLearningRanking = computed(() => {
    // pk 租户隐藏学习排行榜
    return domainName.value !== 'pk'
  })
</script>

<template>
  <div
    class="page-home"
    style="margin-top: 20px"
    :class="{ disasterBg: specialTenantList.includes(domainName), jdBg: domainName === 'jd' }"
  >
    <div class="home-entry">
      <!-- 防灾减灾知识达人评比特殊处理 -->
      <PreventDisasterHome v-if="unepTenantList.includes(domainName)" />
      <!-- 安全发展&防灾减灾学习平台特殊处理 -->
      <Safetydevelopment v-else-if="domainName === 'town'" />
      <!-- 工会促进安全生产学习平台 -->
      <CjaqHome v-else-if="domainName === 'cjaq'" />
      <!-- 嘉定镇街道防灾减灾知识学习（校园版） -->
      <JdHome v-else-if="domainName === 'jd'" />
      <!-- 上海市普陀区企业合同信用促进会 -->
      <PtHome v-else-if="domainName === 'pt'" />
      <!-- 上海市企业首席合同官培训认证平台 -->
      <!-- <template v-else-if="domainName === 'pthtg'">
        <PthtgHome />
      </template> -->
      <!-- 主干 -->
      <template v-else>
        <div class="home-list">
          <!-- Banner图 -->
          <!-- <HomeBanner /> -->
          <div class="home-list-banner"
            ><img src="https://training-voc.obs.cn-north-4.myhuaweicloud.com:443/banner.png" alt=""
          /></div>
          <!-- 平台热门课程 -->
          <HomeHot />
        </div>
        <!-- 推荐课程 -->
        <HomeRecommend />
        <!-- 公益宣传片 -->
        <HomeTrailer />

        <!-- 学习排行榜 -->
        <LearningRanking v-if="shouldShowLearningRanking" />
      </template>
    </div>
  </div>
</template>

<style scoped lang="less">
  .disasterBg {
    height: calc(100vh - 70px);
    background-image: url("@/assets/images/disaster-bg.jpg") !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    margin-top: 0 !important;
  }
  .jdBg {
    height: calc(100vh - 70px);
    background-image: url("https://training-voc.obs.cn-north-4.myhuaweicloud.com:443/jd/jd_bg.png") !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    margin-top: 0 !important;
  }
  .page-home {
    padding: 0px 0 100px 0;
  }

  .home-list {
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    width: 95%;
    .home-list-banner {
      width: 60%;
      height: 450px;
      > img {
        height: 100%;
        width: 100%;
      }
    }
  }
</style>
