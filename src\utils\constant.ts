/*
 * @Description: constant
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-12 08:48:30
 * @LastEditTime: 2025-07-28 13:27:39
 */

export const ruleType = {
  LOGIN_PLATFORM_IPOINTS_RULE: "loginPlatformIPointsRule",
  SIGN_IN_POINTS_RULE: "signInIPointsRule",
  COMPLETING_THE_COURSE_POINTS_RULE: "completingTheCourseIPointsRule",
  OTHER_IPOINTS_RULE: "otherIPointsRule"
}

// 用于调整课程或考试详情页面样式及显示内容/控制是否显示底部Footer/是否调用getBaseRemind获取待办信息
export const specialTenantList = ["unep", "town", "cjaq", "yingji", "eduxd", "jinwaitan", "nf"]

// 使用防灾减灾首页及头部导航栏组件
export const unepTenantList = ["unep", "ying<PERSON>", "eduxd", "jinwaitan", "nf"]

// 生产环境列表
export const productionEnvList = ["production", "hwproduction", "xdproduction"]

// 不需要视频防挂机功能的租户
export const noNeedVideoPreventOnHookTenantList = ["cxgs", "jwt", "nanfang"]

// 不需要头部及底部布局的租户列表
export const noNeedLayOutDomainList = ["hb"]

// 不需要展示考试成绩/分数相关信息的租户
export const noNeedExamScoreDomainList = ["lhsr"]

// 不需要展示头部导航栏系统LOGO的租户列表
export const noNeedSystemLogoDomainList = ["jd", "pthtg", "ctmc"]

// 课程类型
export const COURSE_TYPE = {
  VIDEO_COURSE: "S", // 视频课程
  WORD_COURSE: "W", // 文档课程
  "2D_COURSE": "2", // 2D课程
  "3D_COURSE": "3", // 3D课程
  RECORED_COURSE: "4", // 录播课程
  XJ_COURSE: "X" // 宣教课程
}
// 课程类型对应的文件类型
export const FILE_COURSE_TYPE_MAP = {
  [COURSE_TYPE.VIDEO_COURSE]: ["wmv", "mp4", "flv", "avi", "rmvb", "mpg", "m4v"],
  [COURSE_TYPE.WORD_COURSE]: ["doc", "docx", "pdf", "ppt", "pptx"]
}

export const DEFAULT_COVER =
  "https://training-voc.obs.cn-north-4.myhuaweicloud.com/course_default_cover.png"

export const NO_NEED_HOOK_DOMAIN_LIST = ["yygf", "taibao"] as const
