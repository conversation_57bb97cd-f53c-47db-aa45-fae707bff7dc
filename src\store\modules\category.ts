/*
 * @Description: category store
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2025-07-18 17:26:01
 */
import { defineStore } from "pinia"
import useTenantStore from "./tenant"

// 菜单项类型定义
interface MenuItem {
  id: number
  name: string
  url: string
}

// 租户菜单配置类型
interface TenantMenuConfig {
  [key: string]: {
    hiddenMenus?: number[] // 隐藏的菜单ID列表
    showMenus?: number[] // 显示的菜单ID列表（如果定义了，则只显示这些菜单）
  }
}

//定义category仓库
const useCategoryStore = defineStore("category", {
  state: () => ({
    // 基础菜单列表
    baseCategoryList: [
      {
        id: 1,
        name: "首页",
        url: "/home"
      },
      {
        id: 12,
        name: "学习",
        url: "/study"
      },
      {
        id: 72,
        name: "课程",
        url: "/course"
      },
      {
        id: 73,
        name: "资料",
        url: "/means"
      },
      {
        id: 74,
        name: "通知",
        url: "/news"
      },
      {
        id: 75,
        name: "游戏",
        url: "/game"
      }
    ] as MenuItem[],

    // 租户菜单配置
    tenantMenuConfig: {
      // pk 租户隐藏游戏模块
      pk: {
        hiddenMenus: [75] // 隐藏游戏菜单
      }
    } as TenantMenuConfig
  }),

  getters: {
    // 根据当前租户返回过滤后的菜单列表
    categoryList: state => {
      const tenantStore = useTenantStore()
      const currentDomain = tenantStore.domainName
      console.log("currentDomain", currentDomain)

      // 如果 domainName 为空，则返回所有菜单
      if (!currentDomain) {
        return state.baseCategoryList
      }

      // 获取当前租户的菜单配置
      const config = state.tenantMenuConfig[currentDomain]

      if (!config) {
        // 如果没有配置，返回所有菜单
        return state.baseCategoryList
      }

      let filteredMenus = [...state.baseCategoryList]

      // 如果定义了 showMenus，则只显示指定的菜单
      if (config.showMenus && config.showMenus.length > 0) {
        filteredMenus = filteredMenus.filter(menu => config.showMenus!.includes(menu.id))
      }

      // 如果定义了 hiddenMenus，则隐藏指定的菜单
      if (config.hiddenMenus && config.hiddenMenus.length > 0) {
        filteredMenus = filteredMenus.filter(menu => !config.hiddenMenus!.includes(menu.id))
      }

      return filteredMenus
    }
  },

  actions: {
    // 动态添加或更新租户菜单配置
    updateTenantMenuConfig(domain: string, config: TenantMenuConfig[string]) {
      this.tenantMenuConfig[domain] = config
    },

    // 检查指定菜单是否对当前租户可见
    isMenuVisible(menuId: number): boolean {
      const tenantStore = useTenantStore()
      const currentDomain = tenantStore.domainName
      const config = this.tenantMenuConfig[currentDomain]

      if (!config) {
        return true
      }

      // 如果定义了 showMenus，检查是否在显示列表中
      if (config.showMenus && config.showMenus.length > 0) {
        return config.showMenus.includes(menuId)
      }

      // 如果定义了 hiddenMenus，检查是否在隐藏列表中
      if (config.hiddenMenus && config.hiddenMenus.length > 0) {
        return !config.hiddenMenus.includes(menuId)
      }

      return true
    }
  }
})

export default useCategoryStore
