/*
 * @Description: vite配置文件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2025-07-31 14:42:23
 */
import { fileURLToPath, URL } from "url"
import { defineConfig, loadEnv } from "vite"
import createVitePlugins from "./vite/plugins"

export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())

  return {
    // 配置开发服务器
    server: {
      port: 3003,
      // 其他常用配置项
      open: true, // 帮我们打开浏览器
      cors: true, // 允许开发时 ajax 跨域
      host: "0.0.0.0",
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        "/dev-api": {
          target: "http://*************:1949", // 阮少川本地
          // target: "https://eduapi.bkehs.com", // 生产环境
          changeOrigin: true,
          rewrite: p => p.replace(/^\/dev-api/, "")
        },
        "/stage-api": {
          target: "http://*************:1949",
          changeOrigin: true,
          rewrite: p => p.replace(/^\/stage-api/, "")
        }
      }
    },
    base: "./", // 设置打包路径
    plugins: createVitePlugins(env),
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url))
      }
    },
    css: {
      preprocessorOptions: {
        less: {
          additionalData: `
          @import "@/assets/styles/variables.less";
          @import "@/assets/styles/mixins.less";
        `
        }
      }
    }
  }
})
