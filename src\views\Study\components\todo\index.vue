<!--
 * @Description: 学习计划
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-02 09:24:30
 * @LastEditTime: 2025-07-04 14:04:00
-->

<script setup lang="ts">
  import { fetchTaskMyList, getTaskById } from "@/api/trainingTask"
  import { MessageBox, EditPen, Document, ArrowRight } from "@element-plus/icons-vue"
  import { editRelateNumber, getMeans } from "@/api/trainingTask/means"
  import { TaskType, TaskTypeConfig, getTaskTypeInfo } from "@/types/task"

  const queryParams = ref({
    pageNum: 1,
    pageSize: 999
  })
  const router = useRouter()
  const dataTotal = ref(0)
  const taskList = ref<any[]>([])
  const taskItem = ref<any[]>([])
  const taskListIndex = ref(0)
  const isExist = ref(false)
  const isShowAll = ref(true)
  const expireWarningMsg = ref("")

  // 格式化日期,只保留年月日
  const formatDate = (dateStr: string) => {
    if (!dateStr) return ""
    return dateStr.split(" ")[0]
  }

  // 处理过期提示信息
  const handleExpireWarnings = (taskList: any[]) => {
    // 检查是否所有任务都没有过期项
    const hasExpiredTasks = taskList.some(task => task.expireList?.length > 0)

    if (!hasExpiredTasks) {
      return {
        showWarning: false,
        warningMessage: ""
      }
    }

    // 生成过期提示信息
    const expireMessages = taskList
      .filter(task => task.expireList?.length > 0)
      .map(task => {
        const expireItems = task.expireList
          .map(item => {
            const daysText =
              item.daysUntilExpire >= 0
                ? `还有${item.daysUntilExpire}天过期`
                : `已超期${Math.abs(item.daysUntilExpire)}天`
            return `${item.name}${daysText}`
          })
          .join("、")

        return `您的${task.taskName}任务下的${expireItems}`
      })
      .join("，")

    return {
      showWarning: true,
      warningMessage: expireMessages
    }
  }

  // 获取任务列表
  async function getTaskInfo() {
    const queryData = {
      completionStatus: "0,1",
      pageSize: 999
    }
    const { rows, total } = await fetchTaskMyList(queryData)
    dataTotal.value = total || 0
    taskList.value = rows

    if (!rows) {
      isShowAll.value = false
      return
    }

    // 处理过期提示
    const { showWarning, warningMessage } = handleExpireWarnings(rows)
    isExist.value = !showWarning
    expireWarningMsg.value = warningMessage

    // 如果有任务,获取第一个任务的详情
    if (rows.length > 0) {
      await getTaskList(rows[0].taskId)
    }
  }

  // 获取任务详情
  const getTaskList = async (taskId: string, index?: number) => {
    if (index !== undefined) taskListIndex.value = index
    const { rows } = await getTaskById(taskId, queryParams.value)
    taskItem.value = rows

    // 处理任务项的按钮显示状态
    taskItem.value.forEach(item => {
      if (!item.preconditionLinkList || item.preconditionLinkList.length === 0) {
        // 没有前置条件
        if (!item.completionStatus || item.completionStatus != "3") {
          item.showBtnName = "study"
        } else if (item.completionStatus == "3") {
          item.showBtnName = "view"
        }
      } else {
        // 有前置条件，检查所有前置任务是否完成
        const allPreconditionsCompleted = item.preconditionLinkList.every(
          precondition => precondition.preconditionStatus === '2'
        )

        if (allPreconditionsCompleted) {
          // 所有前置任务已完成，显示正常按钮
          if (!item.completionStatus || item.completionStatus != "3") {
            item.showBtnName = "study"
          } else if (item.completionStatus == "3") {
            item.showBtnName = "view"
          }
        } else {
          // 前置任务未全部完成，显示提示信息
          item.showBtnName = "precondition"
        }
      }
    })
  }

  const filePreviewRef = ref()

  // 跳转处理
  const jumpTo = async (item: any) => {
    let routeData
    switch (item.fieldType) {
      case TaskTypeConfig[TaskType.COURSE].key:
        routeData = router.resolve({
          path: "/course/detail",
          query: { id: item.fieldId, taskId: item.taskId }
        })
        break
      case TaskTypeConfig[TaskType.EXAM].key:
        routeData = router.resolve({
          path: "/prepare",
          query: {
            baseId: item.fieldId,
            arrangeId: item.examArrangeId,
            taskId: item.taskId
          }
        })
        break
      case TaskTypeConfig[TaskType.QUESTIONNAIRE].key:
        routeData = router.resolve({
          path: "/study/questionnaire/doquestionnaire",
          query: {
            questionnaireId: item.questionnaireId,
            questionnaireIssuedId: item.questionnaireIssuedId
          }
        })
        break
      case TaskTypeConfig[TaskType.RESOURCE].key:
        const meansRes = await getMeans(item.fieldId)
        filePreviewRef.value.fileLoad({
          manageAddress: meansRes.data.manageAddress,
          manageId: item.fieldId
        })
        break
    }
    if (routeData) {
      window.open(routeData.href, "_blank")
    }
  }

  // 检查是否临近截止日期
  const isNearDeadline = (date: string) => {
    if (!date) return false
    const deadline = new Date(date).getTime()
    const now = new Date().getTime()
    const diff = deadline - now
    return diff > 0 && diff <= 7 * 24 * 60 * 60 * 1000
  }

  const downloadCountRecord = async manageId => {
    await editRelateNumber({ manageId, downloadNumber: 0 })
  }

  onMounted(() => {
    getTaskInfo()
  })
</script>

<template>
  <div class="flex flex-col h-full overflow-x-hidden" v-if="taskList.length">
    <!-- 上方任务切换栏区域 -->
    <div class="task-container mt-4">
      <el-scrollbar class="task-scrollbar">
        <div class="flex gap-8">
          <div
            v-for="(item, index) in taskList"
            :key="item.taskId"
            class="relative p-6 rounded-lg cursor-pointer transition-all shrink-0 w-[200px] text-white"
            :class="[index === taskListIndex ? 'active-task' : 'bg-[#aaaaaa]']"
            @click="getTaskList(item.taskId, index)"
          >
            <div class="text-18px font-500 mb-2 font-bold truncate">{{ item.taskName }}</div>
            <div class="text-12px opacity-80">截止时间：{{ formatDate(item.endTime) }}</div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 标题区域 -->
    <div
      class="flex items-center justify-between mb-6 relative pb-6 border-b-2 border-[#EBEEF5] border-b-dashed"
    >
      <div class="flex flex-col">
        <div class="text-24px font-bold mb-2 relative pl-3">
          {{ taskList[taskListIndex]?.taskName }}
          <!-- 左侧橙色竖线 -->
          <div class="absolute left-0 top-20% w-1 h-60% bg-[#fea501]"></div>
        </div>
        <div class="text-[#2b2b2c] text-16px flex gap-20">
          <div>
            {{ taskList[taskListIndex]?.startTime }} 至
            {{ taskList[taskListIndex]?.endTime }}
          </div>
          <div class="flex gap-4">
            <span class="text-[#606266] text-16px">进度</span>
            <el-progress
              :percentage="Math.floor(taskList[taskListIndex]?.rateLearning * 100) || 0"
              :stroke-width="20"
              class="w-[700px]"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 任务列表区域 -->
    <div v-if="isShowAll" class="bg-white rounded-lg">
      <!-- 标题栏 -->
      <div class="flex items-center">
        <!-- 本期学习安排 - 梯形背景 -->
        <div class="study-plan-title relative flex items-center w-480px">
          <el-icon class="text-20px mr-2"><Document /></el-icon>
          <span class="text-18px font-bold">本期学习安排</span>
        </div>

        <!-- 温馨提示 -->
        <div
          v-if="!isExist && isShowAll"
          class="bg-[#d9001b] text-[#f5bb07] px-5 py-2 rounded-full flex items-center"
        >
          <span class="font-bold mr-2">温馨提示：</span>
          <span>{{ expireWarningMsg }}</span>
        </div>
      </div>

      <!-- 任务列表 -->
      <div class="border-2 border-solid border-#8ecdff rounded-b-lg rounded-tr-lg">
        <el-scrollbar>
          <div v-for="(item, index) in taskItem" :key="index">
            <div
              class="flex items-center py-6 px-4 rounded-lg transition-colors"
              :style="{
                backgroundColor: `${getTaskTypeInfo(item).color}10`
              }"
            >
              <!-- 左侧类型和名称 -->
              <div class="flex items-center gap-4 min-w-[300px]">
                <div class="flex items-center gap-2">
                  <el-icon :style="{ color: getTaskTypeInfo(item).color }" class="text-20px">
                    <component :is="getTaskTypeInfo(item).icon" />
                  </el-icon>
                  <div class="text-16px">
                    <span :style="{ color: getTaskTypeInfo(item).color }">
                      {{ getTaskTypeInfo(item)?.text }}：
                    </span>
                    <span class="truncate max-w-[400px] inline-block align-bottom">
                      {{
                        item.courseName ||
                        item.questionnaireName ||
                        item.manageName ||
                        item.examName
                      }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 中间进度和截止日期 -->
              <div class="flex items-center ml-auto mr-10">
                <el-progress
                  v-if="item.courseId > 0"
                  :percentage="item.learningProcess * 100 || 0"
                  :stroke-width="16"
                  :color="getTaskTypeInfo(item).color"
                  class="w-[450px] !mr-6"
                />
                <!-- <span
                  v-if="item.offShelfDate"
                  class="text-14px min-w-[200px]"
                  :class="[
                    isNearDeadline(item.offShelfDate)
                      ? 'text-[#f56c6c] animate-pulse'
                      : 'text-[#909399]'
                  ]"
                >
                  截止日期：{{ formatDate(item.offShelfDate) }}
                </span> -->
              </div>

              <!-- 右侧操作按钮 -->
              <el-button
                v-if="item.showBtnName === 'precondition'"
                type="info"
                class="!min-w-[120px]"
                disabled
              >
                请先完成前置任务
              </el-button>
              <el-button
                v-else
                type="primary"
                class="!min-w-[100px]"
                :style="{
                  '--el-button-bg-color': getTaskTypeInfo(item).color,
                  '--el-button-border-color': getTaskTypeInfo(item).color
                }"
                @click="jumpTo(item)"
              >
                {{
                  item.fieldType === "0" ? "去学习" : item.fieldType === "1" ? "去考试" : "去完成"
                }}
                <el-icon class="ml-1"><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <!-- 空状态 -->
    <el-empty v-else description="暂无数据" :image-size="300" />

    <BaseFilePreview
      ref="filePreviewRef"
      @clickDownload="downloadCountRecord"
      :meansList="taskItem"
    />
  </div>
  <el-empty v-else description="暂无数据" :image-size="300" />
</template>

<style lang="less" scoped>
  .active-task {
    background-image: url("https://training-voc.obs.cn-north-4.myhuaweicloud.com/current_learning_schedule.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
  }

  .icon-color-success {
    color: #67c23a;
  }

  .icon-weiwancheng {
    color: #f56c6c;
  }

  :deep(.el-progress-bar__inner) {
    transition: all 0.3s;
  }

  :deep(.el-progress) {
    margin-right: 0;
  }

  // 添加动画
  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .study-plan-title {
    background: url("https://training-voc.obs.cn-north-4.myhuaweicloud.com/current_learning_schedule.png")
      no-repeat center center;
    background-size: 100% 100%;
    color: white;
    padding: 12px 40px 12px 20px;
  }

  // 确保图标垂直居中
  :deep(.el-icon) {
    display: flex;
    align-items: center;
  }

  .task-container {
    position: relative;
    width: 100%;
    height: 130px;
  }

  .task-scrollbar {
    height: 100%;

    :deep(.el-scrollbar__wrap) {
      overflow-x: auto;
      overflow-y: hidden;
    }

    :deep(.el-scrollbar__bar.is-horizontal) {
      height: 8px;
      opacity: 1;

      .el-scrollbar__thumb {
        background-color: #c0c4cc;
        opacity: 0.3;

        &:hover {
          opacity: 0.5;
        }
      }
    }
  }
</style>
